import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // Check for large request bodies on blog API endpoints
  if (request.nextUrl.pathname.startsWith('/api/admin/blog') &&
      (request.method === 'POST' || request.method === 'PUT')) {

    const contentLength = request.headers.get('content-length');

    if (contentLength) {
      const sizeInBytes = parseInt(contentLength);
      const maxSizeInBytes = 10 * 1024 * 1024; // 10MB

      if (sizeInBytes > maxSizeInBytes) {
        console.warn(`Request too large: ${sizeInBytes} bytes (max: ${maxSizeInBytes})`);
        return NextResponse.json(
          {
            success: false,
            error: 'Request too large. Blog content exceeds the maximum allowed size of 10MB. Please reduce content size, compress images, or split into multiple posts.'
          },
          { status: 413 }
        );
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/api/admin/blog/:path*'
  ]
};
